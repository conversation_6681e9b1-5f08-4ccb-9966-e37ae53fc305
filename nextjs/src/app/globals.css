
@tailwind base;
@layer base{
  html{
      @apply h-full font-sans text-font-16;
  }
  body {
    @apply flex  flex-1 h-full font-sans text-font-16 font-normal text-b2 bg-b15;
    font-feature-settings: 'salt' on;
  }
  a{
      @apply transition delay-150;
  }
  .btn{
      @apply inline-block cursor-pointer text-center rounded-custom md:px-6 px-3 py-[9px] text-font-15 leading-[20px] font-semibold border border-solid border-transparent transition duration-150 ease-in-out focus:ring-0 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed [&>svg]:transition [&>svg]:ease-in-out;
  }
  .btn-blue{
      @apply bg-blue border-blue text-white hover:bg-bluehover active:bg-bluehover hover:border-bluehover active:border-bluehover disabled:hover:bg-blue;
  }
  .btn-lightblue{
    @apply bg-bluelight border-bluelight text-white hover:bg-bluehover active:bg-bluehover hover:border-bluehover active:border-bluehover disabled:hover:bg-bluelight [&>svg]:fill-blue [&:hover>svg]:fill-b15 [&:active>svg]:fill-b15;
}
  .btn-outline-blue{
    @apply bg-transparent border-blue text-blue hover:bg-blue active:bg-blue hover:text-white hover:border-blue active:border-blue;
  }
  .btn-gray{
    @apply font-semibold bg-b12 border-b12 text-b5 hover:bg-blue active:bg-blue hover:border-blue active:border-blue hover:text-b15 active:text-b15 [&>svg]:fill-b5 [&>i]:text-b5;
  }
  .btn-black{
    @apply font-semibold bg-black border-black text-white hover:bg-blue active:bg-blue hover:border-blue active:border-blue hover:text-b15 active:text-b15 [&>svg]:fill-white [&>i]:text-b5;
  }
  .btn-dark-gray{
    @apply bg-b10 border-b10 text-white [&>svg]:fill-b2 [&>i]:text-b2 hover:bg-b7  active:bg-blue hover:border-b7  active:border-blue hover:text-white  active:text-white [&:hover>svg]:fill-white  [&:active>svg]:fill-white [&:hover>i]:fill-white [&:active>i]:fill-white;
  }
  .btn-green{
    @apply bg-green border-green text-b15 hover:bg-success-600 active:bg-success-600 hover:border-success-600 active:border-success-600 hover:text-b15 active:text-b15;
  }
  .btn-white{
    @apply bg-b15 border-b15 text-b2 hover:bg-b1 active:bg-b1 hover:border-b1 active:border-b1 hover:text-b15 active:text-b15;
  }
  .btn-outline-black{
    @apply bg-transparent border-b2 text-b2 hover:bg-b2 hover:border-b2 hover:text-b15;
  }
  .btn-outline-white{
    @apply bg-transparent border-white text-white hover:bg-white hover:border-white hover:text-blue;
  }
  .btn-outline-gray{
    @apply bg-transparent border-b10 text-b2 hover:bg-b2 hover:border-b2 hover:text-b15 [&>svg]:fill-b2 [&>i]:text-b2  [&:hover>svg]:fill-white [&:active>svg]:fill-white;
  }
  .btn-red{
    @apply bg-reddark border-reddark text-b15 hover:bg-reddarkhover active:bg-reddarkhover hover:border-reddarkhover active:border-reddarkhover hover:text-b15 active:text-b15;
  }
  .btn-lg{
      @apply px-6 py-1.5 lg:py-[12px] lg:px-6 text-font-16 font-bold leading-normal;
  }
  .transparent-ghost-btn{
    @apply p-1 text-center flex justify-center items-center transition duration-150 ease-in-out hover:bg-b11 [&[data-state=open]]:bg-b11 active:bg-b11 focus:outline-none active:outline-none focus-within:outline-none;
  }
  .btn-round {
    @apply w-[25px] h-[25px] min-w-[25px] rounded-full;
  }
  .btn-round-icon{
    @apply [&>svg]:h-[13px] [&>svg]:w-[13px] [&>svg]:object-contain [&>svg>path]:fill-b6 [&>svg>circle]:fill-b6;
  }
}

@tailwind components;
@tailwind utilities;


:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

[role=tooltip] { /* hide tooltips by default */
  > div{
    @apply !p-1 !px-2 !text-font-13 !bg-b3;
  }
 
}

.markdown {
  max-width: none
}
.markdown ol li p{
  margin: 0;
}

.markdown h1 {
  font-weight: 600
}

.markdown h1:first-child {
  margin-top: 0
}

.markdown h2 {
  font-weight: 600;
  margin-bottom: 1rem;
  margin-top: 2rem
}

.markdown h2:first-child {
  margin-top: 0
}

.markdown h3 {
  font-weight: 600;
  margin-bottom: .5rem;
  margin-top: 1rem
}

.markdown h3:first-child {
  margin-top: 0
}

.markdown h4 {
  font-weight: 600;
  margin-bottom: .5rem;
  margin-top: 1rem
}

.markdown h4:first-child {
  margin-top: 0
}

.markdown h5 {
  font-weight: 600
}

.markdown h5:first-child {
  margin-top: 0
}
.markdown a{
  @apply text-blue !important;
}
.markdown a:hover{
  @apply underline !important;
}
.markdown blockquote {
  --tw-border-opacity: 1;
  border-color: rgba(155,155,155,var(--tw-border-opacity));
  border-left-width: 2px;
  line-height: 1.5rem;
  margin: 0;
  padding-bottom: .5rem;
  padding-left: 1rem;
  padding-top: .5rem
}

.markdown blockquote>p {
  margin: 0
}

.markdown blockquote>p:after,.markdown blockquote>p:before {
  display: none
}

.markdown table {
  --tw-border-spacing-x: 0px;
  --tw-border-spacing-y: 0px;
  border-collapse: separate;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
  margin-bottom: .25rem;
  margin-top: .25rem;
  width: 100%
}

.markdown th {
  background-color: rgba(0,0,0,.1);
  background-color: var(--border-light);
  border-bottom-width: 1px;
  border-color: rgba(0,0,0,.15);
  border-color: var(--border-medium);
  border-left-width: 1px;
  border-top-width: 1px;
  padding: .25rem .75rem
}

.markdown th:first-child {
  border-top-left-radius: .375rem
}

.markdown th:last-child {
  border-right-width: 1px;
  border-top-right-radius: .375rem
}

.markdown td {
  border-bottom-width: 1px;
  border-color: rgba(0,0,0,.15);
  border-color: var(--border-medium);
  border-left-width: 1px;
  padding: .25rem .75rem
}

.markdown td:last-child {
  border-right-width: 1px
}

.markdown tbody tr:last-child td:first-child {
  border-bottom-left-radius: .375rem
}

.markdown tbody tr:last-child td:last-child {
  border-bottom-right-radius: .375rem
}

.markdown a {
  color: #2964aa;
  color: var(--link);
  font-weight: 400;
  text-decoration-line: none
}

.markdown a:hover {
  color: #749ac8;
  color: var(--link-hover)
}
.markdown pre, .markdown .language-text{
  margin: 0;
}

::-webkit-scrollbar-track {
  @apply bg-transparent rounded-full
}
::-webkit-scrollbar-thumb {
  @apply border border-transparent rounded-full bg-b10;
}
::-webkit-scrollbar {
  @apply w-1.5 h-2;
}


.radio-input-check{
  @apply relative h-6 w-6 appearance-none rounded-[0.25rem] border border-solid border-b10 outline-none before:pointer-events-none before:absolute before:h-6 before:w-6 before:scale-0 before:rounded-full before:bg-transparent before:opacity-0  before:shadow-transparent before:content-[''] checked:border-blue checked:bg-blue checked:before:opacity-[0.16] checked:after:absolute checked:after:mt-0.5 checked:after:ms-2 checked:after:block checked:after:h-[0.8125rem] checked:after:w-[0.375rem] checked:after:rotate-45 checked:after:border-[0.125rem] checked:after:border-l-0 checked:after:border-t-0 checked:after:border-solid checked:after:border-white checked:after:bg-transparent checked:after:content-[''] hover:cursor-pointer hover:before:opacity-[0.04] hover:before:shadow-black/60 focus:shadow-none focus:transition-[border-color_0.2s] focus:before:scale-100 focus:before:opacity-[0.12] focus:before:shadow-black/60 focus:before:transition-[box-shadow_0.2s,transform_0.2s] focus:after:absolute focus:after:z-[1] focus:after:block focus:after:h-[0.875rem] focus:after:w-[0.875rem] focus:after:rounded-[0.125rem] focus:after:content-[''] checked:focus:before:scale-100 checked:focus:before:transition-[box-shadow_0.2s,transform_0.2s] checked:focus:after:mt-0.5 checked:focus:after:ms-2 checked:focus:after:h-[0.8125rem] checked:focus:after:w-[0.375rem] checked:focus:after:rotate-45 checked:focus:after:rounded-none checked:focus:after:border-[0.125rem] checked:focus:after:border-l-0 checked:focus:after:border-t-0 checked:focus:after:border-solid checked:focus:after:border-white checked:focus:after:bg-transparent rtl:float-right;
  &.radio-input-check-md{
     @apply h-[18px] w-[18px] before:h-[18px] before:w-[18px] checked:after:mt-px checked:after:ms-[5px] checked:after:h-[0.625rem] checked:after:w-[0.375rem] checked:focus:after:mt-px checked:focus:after:ms-[5px] checked:focus:after:h-[0.625rem] checked:focus:after:w-[0.375rem] focus:before:scale-75 checked:focus:before:scale-75
  }
}
.default-form-input{
  @apply block min-h-[auto] w-full rounded-custom border border-b9 focus:border-b2 bg-transparent px-[15px] py-[12px] text-font-14 font-normal outline-none transition-all duration-200 ease-linear motion-reduce:transition-none text-b2 placeholder:text-b7;
  &.default-form-input-border-light{
    @apply border-b9 focus:border-b2 placeholder:text-b7;
  }
  &[readonly], &[disabled]{
    @apply bg-b12;
  }
  &[disabled]{
    @apply cursor-not-allowed;
  }
}
.default-form-input-md{
  @apply text-font-14 leading-[1.4] px-3 py-[8px] h-10;
}
body .MuiChipsInput-TextField{
  .MuiOutlinedInput-notchedOutline{
    @apply border border-b7 hover:border-b7 focus:border-blue;
  }
  .Mui-focused{
    .MuiOutlinedInput-notchedOutline{
      @apply border border-blue;
    }
  }
  &:hover{
    .MuiOutlinedInput-notchedOutline{
      @apply border-b7;
    }
  }
  
}
.search-wrap{
  .default-form-input{
    @apply pl-10;
  }
}
.input-checkbox{
  @apply relative float-left -ms-8 me-[6px] mt-[0.15rem] h-6 w-6 appearance-none rounded-[0.25rem] border border-solid border-b8 outline-none before:pointer-events-none before:absolute before:h-[22px] before:w-[22px] before:scale-0 before:rounded-full before:bg-transparent before:opacity-0 before:shadow-transparent before:content-[''] checked:border-blue checked:bg-blue checked:before:opacity-[0.16] checked:after:absolute checked:after:mt-0.5 checked:after:ms-2 checked:after:block checked:after:h-[0.8125rem] checked:after:w-[0.375rem] checked:after:rotate-45 checked:after:border-[0.125rem] checked:after:border-l-0 checked:after:border-t-0 checked:after:border-solid checked:after:border-white checked:after:bg-transparent checked:after:content-[''] hover:cursor-pointer hover:before:opacity-[0.04] hover:before:shadow-black/60 focus:shadow-none focus:transition-[border-color_0.2s] focus:before:scale-100 focus:before:opacity-[0.12] focus:before:shadow-black/60 focus:before:transition-[box-shadow_0.2s,transform_0.2s] focus:after:absolute focus:after:z-[1] focus:after:block focus:after:h-[22px] focus:after:w-[22px] focus:after:rounded-[0.125rem] focus:after:content-[''] checked:focus:before:scale-100  checked:focus:before:transition-[box-shadow_0.2s,transform_0.2s] checked:focus:after:mt-0.5 checked:focus:after:ms-2 checked:focus:after:h-[0.8125rem] checked:focus:after:w-[0.375rem] checked:focus:after:rotate-45 checked:focus:after:rounded-none checked:focus:after:border-[0.125rem] checked:focus:after:border-l-0 checked:focus:after:border-t-0 checked:focus:after:border-solid checked:focus:after:border-white checked:focus:after:bg-transparent rtl:float-right ;
}
.group-button{
  @apply absolute transition duration-150 ease-in-out -right-3 -top-3 h-7 w-7 p-[5px] appearance-none rounded-full checked:bg-white checked:before:content-[''] checked:before:absolute checked:before:w-[18px] checked:before:h-[18px] checked:before:rounded-full checked:before:border-[2px] checked:before:border-green checked:after:absolute checked:after:mt-1 checked:after:ms-[7px] checked:after:block checked:after:h-2 checked:after:w-[5px] checked:after:rotate-45 checked:after:border-[0.125rem] checked:after:border-l-0 checked:after:border-t-0 checked:after:border-solid checked:after:border-green checked:after:bg-transparent checked:after:content-[''];
}
.PhoneInputInput{
  @apply outline-none border-0;
}
.react-select-container {
  .react-select__control{
    @apply border-b9 rounded-custom h-[40px] bg-transparent shadow-none focus:border-b2 focus-visible:border-b2 text-font-14 overflow-hidden;
    .react-select__value-container{
      @apply px-[15px] text-font-14;
      max-height: 40px;
      overflow-y: auto;
      .react-select__single-value{
          @apply text-font-14 leading-[1.4] font-normal outline-none transition-all duration-200 ease-linear motion-reduce:transition-none text-b2;
      }
    }
    .react-select__indicators{
      align-items: flex-start;
      .react-select__indicator{
        &.react-select__clear-indicator{
          
          margin-right: 5px;
          cursor: pointer;
        }
      }
      .react-select__indicator-separator{
        @apply hidden;
      }
      .react-select__dropdown-indicator{
        @apply w-[30px] h-11 py-3.5 pl-0 pr-3.5 bg-[url('../../public/down-arrow.svg')] bg-origin-content bg-no-repeat bg-center [&>svg]:hidden;
        background-size: 14px;
        height: 100%;
        cursor: pointer;
      }
    }
  }
  &.b-white{
    .react-select__control{
      @apply bg-b15;
    }
  }
  
  .react-select__menu{
    @apply mt-0 border border-b10 shadow-[0_10px_15_0_rgba(0,0,0,0.1)] rounded-t-none rounded-custom;
    .react-select__menu-list{
      .react-select__option{
          @apply border-b border-b10 text-font-14 text-b2 hover:bg-b12 focus:bg-b12 active:bg-b12 transition-all ease-in-out last:border-b-0;
          &.react-select__option--is-selected, &.react-select__option--is-focused{
            @apply bg-b12;
          }
      }
    }
  }
  &.react-select-sm{
    .react-select__control{
      @apply min-h-10;
      .react-select__indicators{
        .react-select__dropdown-indicator{
          @apply w-[30px] h-[38px] py-2.5;
        }
      }
    }
  }
  &.react-select-border-light{
    .react-select__control{
      @apply border-b10 focus:border-b2 placeholder:text-b7;
      &.react-select__control--menu-is-open{
        @apply border-b2;
      }
    }
  }
}
.react-datepicker__input-container{
  input{
    @apply block min-h-[auto] w-full rounded-custom border border-b7 focus:border-blue bg-transparent px-[15px] py-[0.8125rem] text-font-16 leading-[1.4] font-normal outline-none transition-all duration-200 ease-linear motion-reduce:transition-none text-b2 placeholder:text-b5;
  }
}
.check-list{
  @apply list-none pl-0;
  li{
    @apply relative mb-3 text-font-14 leading-normal pl-6 after:content-[''] after:bg-[url('../../public/check-black.svg')] after:bg-contain after:bg-origin-content after:bg-no-repeat after:bg-center after:w-[15px] after:h-2.5 after:absolute after:left-0 after:top-1.5 last:mb-0;
    a{
      @apply underline;
    }
  }
}
 .border-heading{
  @apply flex items-center text-center;
    &:before, &:after{
      @apply content-[''] h-px bg-b8 flex-grow shrink-0 basis-1/5;
    }
    &:before {
      @apply mr-4;
    }
    &:after {
      @apply ml-4;
    }
 }

.welcome-slider{
  .slick-dots{
      @apply -bottom-10;
      text-align: left;
      li{
        @apply w-3 h-3 mx-1;
        button{
          @apply p-0 w-3 h-3 before:w-3 before:h-3 before:content-[''] before:bg-b15 before:rounded-full before:opacity-100 before:transition-all before:duration-150 before:ease-in-out;
        }
        &.slick-active{
          button{
            @apply before:bg-blue;
          }
        }
      }
  }
}


.prompts-item-detail:hover .transparent-ghost-btn{
  @apply bg-white;
}
.prompts-items{
  .prompts-item-detail{
    .dropdown-action{
      > svg {
        @apply  fill-b6;
      }
    }
    
    &:hover{
      .dropdown-action{
        @apply hover:bg-b15 focus:bg-b15 active:bg-b15;
        > svg {
          @apply  fill-b15;
        }
        &:hover, &:focus, &:active{
          @apply bg-b15;
          > svg {
            @apply  fill-blue;
          }
        }

      }
    }
  }
}
.prompts-items-grid{
  .prompts-item-heading{
      h5{
        @apply w-[calc(100%-35px)];
      }
  }
  .prompts-items-dropdown{
    @apply absolute top-0 right-0;
  }
}

.chat-items{
  .chat-item-detail{
    .dropdown-action{
      > svg {
        > circle{
          @apply  fill-b6;
        }
        
      }
    }
    &:hover{
      .dropdown-action{
        @apply hover:bg-b15 focus:bg-b15 active:bg-b15;
        > svg {
          > circle{
            @apply  fill-b15;
          }
        }
        &:hover, &:focus, &:active{
          @apply bg-b15;
          > svg {
            @apply  fill-blue;
            > circle{
              @apply  fill-blue;
            }
          }
        }
  
      }
    }
  }
  
}

.range-slider {
  @apply w-full h-4 bg-blue/15 rounded-lg outline-none overflow-hidden appearance-none cursor-pointer
}

.range-slider::-webkit-slider-thumb {
  @apply w-4 h-4 rounded-full bg-blue cursor-pointer appearance-none shadow-[-407px_0_0_400px_rgba(80,101,246,1)]
}

.thank-you-bg{
  background-image: url(../../public/thank-you-bg.svg);
}
.CardElement {
  height: 40px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
}

.StripeElement {
  @apply block min-h-[auto] w-full rounded-custom border border-b7 focus:border-blue bg-transparent px-[15px] py-[0.8125rem] text-font-16 leading-[1.4] font-normal outline-none transition-all duration-200 ease-linear motion-reduce:transition-none text-b2 placeholder:text-b5;
}

.gpt-sidebar{
  li{
    position: relative;
    &::after{
      @apply content-[''] transition-all duration-150 ease-in-out opacity-0 absolute -right-0 top-1/2 -translate-y-1/2 w-0 h-0 border-transparent border-solid border-b-[10px] border-t-[10px] border-l-[12px] border-l-b2; 
    @apply xl:block hidden
    }
    &.active{
      &::after{
        @apply opacity-100 -right-2.5;
      }
    }
  }
}

.default-content{
  @apply font-normal text-b2 text-font-16 leading-7;
  p, ul, li, ol, a:not(.btn), span{
    @apply font-normal text-b2 text-font-16 leading-7;
      > a:not(.btn){
          @apply text-b2 underline hover:text-blue;
      }
  }
  strong, b{
      @apply font-semibold;
  }
  ul{
      @apply text-left pl-0 list-none my-3 first:mt-0 last:mb-0;
      li {
          @apply mb-[3px] last:mb-0 pl-5 relative after:absolute after:left-0 after:top-[9px] after:w-2 after:h-3 after:bg-no-repeat after:bg-center after:bg-contain after:bg-[url('../../public/right-arrow-black.svg')];
      }
  }
  ol{
      counter-reset: li;
      @apply text-left p-0 mx-0 my-3 first:mt-0 last:mb-0;
      > li{
          @apply relative list-inside pl-5 mb-0.5 last:mb-0;
          &::marker{
              display: none;
              content: none;
          }
          &:before {
              content: counters(li, '.') '. ';
              counter-increment: li;
              @apply absolute left-0 top-0;
          }
      }
  }
  p{
      + p{
          @apply mt-3;
      }
  }
  p + .btn{
      @apply mt-2.5;
  }
  h1, h2, h3, h4, h5, h6{
    @apply text-b2 mb-[15px] mt-[15px] lg:mt-[30px] first:mt-0 last:mb-0;
  }

  &.default-content-sm{
      @apply text-font-14 leading-6;
      p, ul, li, ol, a:not(.btn), span{
          @apply text-font-14 leading-6;
      }
      ul{
        li {
            @apply mb-0.5 pl-[18px] after:top-[7px] after:w-1.5 after:h-2.5;
        }
    }
  }

}
.table{
  @apply w-full min-w-full text-left text-font-14 font-normal text-b5 whitespace-normal; 
  thead, tbody, tfoot{
    tr{
      @apply border-b border-b11;
      td, th{
        @apply font-normal py-3 px-4;
      }
    }
  }
  thead{
    tr{
      th{
        @apply font-semibold;
      }
      td, th{
        @apply text-b2;
      }
    }
  }
  tbody{
    tr{
      @apply last:border-b-0 hover:bg-b12 transition-all ease-in-out;
      td, th{
        @apply font-normal text-b5;
        a{
          @apply text-blue hover:text-bluehover no-underline;
        }
      }
    }
  }
  &.remove-left-right-padding{
    thead, tbody, tfoot{
      tr{
        td, th{
          @apply first:pl-0 last:pr-0;
        }
      }
    }
  }
}
.top-position{
  top: 50%;
  transform: translateY(-50%);
}

.dot-flashing {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #9880ff;
  color: #9880ff;
  animation: dot-flashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}
.dot-flashing::before, .dot-flashing::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}
.dot-flashing::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #9880ff;
  color: #9880ff;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 0s;
}
.dot-flashing::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #6637EC;
  color: #9880ff;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dot-flashing {
  0% {
    background-color: #9880ff;
  }
  50%, 100% {
    background-color: rgba(152, 128, 255, 0.2);
  }
}

/* Page Turn Loader */
@keyframes pageTurn {
 0% {
   transform: rotateY(0deg);
 }
 40% {
   transform: rotateY(180deg);
 }
 100% {
   transform: rotateY(180deg);
 }
}

.book {
 padding: 2rem;
 perspective: 37.5rem;
 position: relative;
 width: 118px;
 height: 85px;
 transform: translate3d(0, 0, 0);
 transform-style: preserve-3d;
 margin-bottom: 10px;
}

.page {
 position: absolute;
 width: 55px;
 height: 75px;
 left: 0.25rem;
 top: 0.25rem;
 border: 2.8px solid #B895FE;
 background-color: #FFFFFF;
 background-image: repeating-linear-gradient(
   #B895FE 0 0.125rem,
   hsla(223, 10%, 10%, 0) 0.125rem 0.5rem
 );
 background-repeat: no-repeat;
 background-position: center;
 background-size: 2.5rem 4.125rem, 100% 100%;
 transform-origin: 100% 50%;
 transform-style: preserve-3d;
   transform: translate3d(0, 0, 0);
 &:not(.backPage) {
   border-right-width: 1px;
 }
}

.backPage {
 left: 50%;
 border-left-width: 1px;
}

.pageFlip:nth-of-type(2) {
 position: absolute;
 z-index: 30;
 animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 0 infinite;
}

.pageFlip:nth-of-type(3) {
 position: absolute;
 z-index: 20;
 animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 1.2s infinite;
}

.pageFlip:nth-of-type(4) {
 position: absolute;
 z-index: 10;
 animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 1s infinite;
}
.break-words{
  word-break: break-word;
}
.ProgressRoot{
  @apply mt-1 bg-b11;
}
.ProgressRoot .bg-primary{
  @apply bg-blue
}

.no-thumb .thumb-slider{
  display: none;
}

.chat-content .markdown.prose h3 + p img{
  width: 32.5%;
  margin: 0 1px;
  vertical-align: baseline;
  display: inline-flex;
}
.regenerate-response:hover span{
  display: block;
}
.prose {
  @apply max-md:text-font-14;
}
.prose code::before,
.prose code::after {
  content: none !important;
}
@keyframes progressBar {
  0% {
    transform: translateX(-100%);
    @apply w-0;
  }
  100% {
    transform: translateX(100%);
    @apply w-full
  }
}

/* Add custom animation class in Tailwind */
.animate-progress {
  animation: progressBar 0.6s linear infinite;
}
@keyframes blink {
  0%, 80%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}
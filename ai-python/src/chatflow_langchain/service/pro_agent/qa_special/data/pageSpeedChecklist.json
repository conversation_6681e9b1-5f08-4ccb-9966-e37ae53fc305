[{"Category": "Performance", "Checklist Item": "First Meaningful Paint (FMP) is optimized", "Prompt": "Measure First Meaningful Paint using Lighthouse or PageSpeed. Optimize it to improve perceived performance."}, {"Category": "Performance", "Checklist Item": "Lists all images above 100KB and flags them for", "Prompt": "Ensure that no individual image exceeds 100KB in file size. Large images significantly impact page load time and Core Web Vitals.\nFor every image larger than 100KB, list its URL for review and optimization.\n\nThis data can be pulled directly from the PageSpeed Insights JSON or tools like Lighthouse, WebPageTest, or your own image audit script."}]
[{"Category": "SEO", "Checklist Item": "Structured data (JSON-LD) present", "Prompt": "Check if JSON-LD structured data is implemented for better SERP enhancements."}, {"Category": "SEO", "Checklist Item": "Robots meta tag", "Prompt": "Flag pages with noindex if they should appear in search engines."}, {"Category": "SEO", "Checklist Item": "Verify SEO Plugin Integration", "Prompt": "Scan the page source for evidence of SEO plugin integration. This may include HTML comments, specific meta tags, or structured data that certain plugins typically insert.\n\nCheck for signs of the following SEO plugins:\n\n🔹 Yoast SEO\nLook for an HTML comment like:\n<!-- This site is optimized with the Yoast SEO plugin -->\n\nCommon Yoast-injected tags:\n<meta name=\"robots\" content=\"index, follow\">\n\nOpen Graph meta tags:\n<meta property=\"og:locale\">\n<meta property=\"og:title\">\n\nTwitter meta tags:\n<meta name=\"twitter:card\">\n<meta name=\"twitter:title\">\n\n⚠️ Note: The presence of Open Graph or Twitter meta tags alone does not confirm Yoast usage, as other tools may generate similar tags.\n\n🔹 Rank Math SEO\nLook for an HTML comment like:\n<!-- Rank Math WordPress SEO plugin -->\n\nCheck for JSON-LD structured data (e.g., <script type=\"application/ld+json\">) with values like:\n\"@type\": \"Organization\"\n\"@type\": \"WebPage\"\n\"@type\": \"BreadcrumbList\"\n\n🔹 All in One SEO\nLook for comments such as:\n<!-- All in One SEO Pack --> or\n<!-- AIOSEO - The original SEO plugin for WordPress -->\n\nMeta tags often include:\n<meta name=\"generator\" content=\"All in One SEO\">\n\nSimilar Open Graph and Twitter cards as Yoast or Rank Math\n\nIf none of the above indicators are found, flag the SEO plugin as missing or undetectable."}, {"Category": "SEO", "Checklist Item": "Check if robots.txt exists", "Prompt": "Verify that the robots.txt file is present at the root of the domain (e.g., https://example.com/robots.txt). If the file returns a 200 OK status, it exists. If it returns 404 or is blocked, flag it as missing or inaccessible."}, {"Category": "SEO", "Checklist Item": "Check if robots.txt is SEO-friendly", "Prompt": "Analyze the contents of robots.txt. Ensure it allows access to essential content for search engines, especially Googlebot. Flag it if it blocks important areas like /, /wp-content/, or /wp-json/ unless intentional."}, {"Category": "SEO", "Checklist Item": "Canonical tag (<link rel=\"canonical\">) present", "Prompt": "Ensure canonical tags are present to prevent duplicate content issues."}, {"Category": "SEO", "Checklist Item": "Test XML Sitemap", "Prompt": "Attempt to access /sitemap.xml and /wp-sitemap.xml. Confirm that at least one of them returns a 200 OK status and valid XML content with sitemap indexes or URLs. If none are found or the XML is invalid, flag it as missing or broken. Also check for excessive redirects or 404s."}, {"Category": "SEO", "Checklist Item": "Check for meta description. Verify presence of <meta name='description'>.", "Prompt": "Ensure a <meta name=\"description\"> tag is present in the <head> section.\nAlso, ensure that the meta description provides a concise and meaningful summary of the page content, optimizing for search engine visibility and click-through rate.\n\nA good meta description should:\n\nClearly reflect the page’s topic or purpose\n\nInclude relevant keywords (naturally)\n\nEncourage users to click through from search results"}, {"Category": "SEO", "Checklist Item": "Ensure 'Discourage search engines' is unchecked", "Prompt": "Check that the 'Discourage search engines from indexing this site' option in Settings > Reading is unchecked. If it’s checked, the site will tell crawlers not to index it, preventing it from appearing in search results. Check the page source for a meta tag like <meta name='robots' content='noindex, nofollow'>. If found, this suggests the WordPress 'Discourage search engines' setting is enabled. Also, check /robots.txt for Disallow: /, which may indicate the same. Flag the site if either condition is met."}, {"Category": "SEO", "Checklist Item": "Check for Open Graph meta tags. Verify presence of og:title, og:description, og:image, etc.", "Prompt": "Ensure Open Graph meta tags are included for optimized social media sharing."}, {"Category": "SEO", "Checklist Item": "Check for Twitter Card meta tags. Verify presence of twitter:card, twitter:title, etc.", "Prompt": "Ensure Twitter Card meta tags are set for better content previews on Twitter."}, {"Category": "Accessibility", "Checklist Item": "Check for <html lang='...'>. Ensure <html> includes lang attribute.", "Prompt": "Ensure the <html> tag includes a lang attribute for screen readers and localization."}, {"Category": "Accessibility", "Checklist Item": "Check for missing alt attributes. Flag <img> tags without alt attribute.", "Prompt": "Ensure all images have descriptive alt tags for accessibility and image SEO."}, {"Category": "Accessibility", "Checklist Item": "Check heading hierarchy (H1, H2, H3). Ensure logical heading order.", "Prompt": "Ensure that headings follow semantic order: one <h1>, followed by properly nested <h2>s."}, {"Category": "Accessibility", "Checklist Item": "Images have alt attributes", "Prompt": "Ensure every <img> tag has a descriptive 'alt' attribute."}, {"Category": "Accessibility", "Checklist Item": "ARIA attributes or landmarks", "Prompt": "Verify the use of ARIA roles and landmarks for better screen reader support."}, {"Category": "Accessibility", "Checklist Item": "<noscript> tag provides fallback content", "Prompt": "Ensure a <noscript> tag exists"}, {"Category": "UX", "Checklist Item": "Ensure copyright year is the 2025.", "Prompt": "Ensure the copyright year should be 2025. If its 2025 then Pass and if not then Fail."}, {"Category": "UX", "Checklist Item": "Verify social media icons are associated with the correct accounts", "Prompt": "Ensure that social media share or follow icons/buttons link to the official and correct brand/company social profiles (e.g., Twitter, LinkedIn, Instagram, Facebook)."}, {"Category": "UX", "Checklist Item": "Verify that the company/website logo is linked to the homepage", "Prompt": "Ensure the logo in the website header links back to the homepage to improve navigational consistency and user experience."}, {"Category": "UX", "Checklist Item": "External links open in a new tab (target='_blank' rel='noopener noreferrer')", "Prompt": "Ensure all external <a> links use target='_blank' and include rel='noopener noreferrer' for security and user experience."}, {"Category": "Security", "Checklist Item": "Ensure each font-family declaration has a fallback font (e.g., sans-serif).", "Prompt": "Flag large inline <script> blocks over 50KB. Recommend moving them to external files or splitting them up."}, {"Category": "Security", "Checklist Item": "Detect non-secure JS sources. Find <script> tags using http:// instead of https://.", "Prompt": "Check all <script> tags to ensure they use HTTPS. Flag any insecure HTTP sources."}, {"Category": "Security", "Checklist Item": "HTTPS is enforced for all resources", "Prompt": "Ensure all resource URLs (scripts, styles, images) are loaded over HTTPS."}, {"Category": "Security", "Checklist Item": "No visible API keys or sensitive data in page source", "Prompt": "Ensure no visible API keys, secrets, or tokens are present in the HTML or JS source."}, {"Category": "Security", "Checklist Item": "Check if Google reCAPTCHA is used when a form is present", "Prompt": "Check if the page includes Google reCAPTCHA script when a form is detected. If reCAPTCHA Exists than Pass and if it doesn’t exist than Fail"}, {"Category": "Code Quality", "Checklist Item": "Detect duplicate script includes", "Prompt": "Scan for duplicate <script src=''> URLs and flag repeated inclusions."}, {"Category": "Code Quality", "Checklist Item": "Avoid use of !important in CSS", "Prompt": "Ensure '!important' is not used in styles unless absolutely necessary."}, {"Category": "Code Quality", "Checklist Item": "Avoid inline styles", "Prompt": "Check if inline 'style=' attributes are used and flag if excessive."}, {"Category": "Code Quality", "Checklist Item": "No duplicate or unnecessary IDs in the DOM", "Prompt": "Check the DOM for repeated id attributes. Each ID must be unique to avoid conflicts and accessibility issues."}, {"Category": "Content Quality", "Checklist Item": "Scan visible text for grammar and spelling errors.", "Prompt": "Review the page for grammar and spelling errors to ensure content professionalism."}, {"Category": "Responsive", "Checklist Item": "Ensure viewport is set for mobile devices.", "Prompt": "Make sure the page has a viewport meta tag to ensure mobile responsiveness."}, {"Category": "HTML Best Practices", "Checklist Item": "Check for charset meta tag. Ensure <meta charset='UTF-8'> is present.", "Prompt": "Verify the character encoding is set properly using the charset meta tag."}, {"Category": "HTML Best Practices", "Checklist Item": "Check for incorrect punctuation (apostrophes, quotes, hyphens/dashes)", "Prompt": "Ensure that text content on the page uses correct punctuation:\n\nCurly quotes (“ ” and ‘ ’) instead of straight quotes (\" and ')\n\nProper apostrophes (’ instead of ')\n\nEn dashes (–) and em dashes (—) instead of hyphens (-) when contextually appropriate"}, {"Category": "Compliance", "Checklist Item": "Check if Privacy Policy link exists. Search for footer link to privacy page.", "Prompt": "Ensure a visible link to your Privacy Policy exists, typically in the footer."}, {"Category": "Compliance", "Checklist Item": "Check if Terms link exists. Search for footer link to terms page.", "Prompt": "Ensure a Terms of Service or Terms & Conditions page is linked."}, {"Category": "Compliance", "Checklist Item": "Check for cookie consent banner. Detect cookie banner or consent script.", "Prompt": "Verify that a cookie consent popup appears on first visit and handles consent properly."}, {"Category": "Analytics", "Checklist Item": "Check for Facebook Pixel script", "Prompt": "Ensure that Facebook Pixel tracking script is loaded if used for retargeting."}, {"Category": "Analytics", "Checklist Item": "Check if Google Analytics script is present", "Prompt": "Check the page source for the presence of the Google Analytics tracking script. Look for a script tag that loads https://www.googletagmanager.com/gtag/js and a gtag('config', 'G-XXXXXXXXXX') call in the following script block. If not found, flag it as missing."}, {"Category": "Performance", "Checklist Item": "Check if favicon is present. Verify <link rel='icon'> exists.", "Prompt": "Verify that a favicon is set for better user experience and brand recognition."}, {"Category": "Performance", "Checklist Item": "Check if scripts are minified. Verify .js filenames and look for compact, whitespace-free code.", "Prompt": "Verify that JavaScript files are minified (e.g., .min.js, no line breaks or whitespace)."}, {"Category": "Performance", "Checklist Item": "Lazy loading of images", "Prompt": "Verify that images use lazy loading to defer offscreen image loading."}, {"Category": "Performance", "Checklist Item": "Ensure defer or async used on scripts", "Prompt": "Confirm all JavaScript files use defer or async to avoid blocking page rendering."}, {"Category": "Performance", "Checklist Item": "Lazy loading is enabled for images", "Prompt": "Ensure that <img> tags include 'loading=\"lazy\"' for performance optimization."}, {"Category": "Performance", "Checklist Item": "Ensure scripts use async or defer attributes", "Prompt": "Check that all external <script> tags use 'async' or 'defer'."}, {"Category": "Performance", "Checklist Item": "Count third-party script usage", "Prompt": "Count external <script> tags from third-party domains (Google, FB, etc.). Flag if excessive."}]
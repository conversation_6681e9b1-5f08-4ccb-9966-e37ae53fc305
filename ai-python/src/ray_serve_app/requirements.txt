aiohttp==3.9.5
aiohttp-cors==0.7.0
aiorwlock==1.4.0
aiosignal==1.3.1
annotated-types==0.6.0
anyio==4.3.0
async-timeout==4.0.3
attrs==23.2.0
blessed==1.20.0
cachetools==5.3.3
certifi==2024.2.2
charset-normalizer==3.3.2
click==8.1.7
cloudpickle==3.0.0
cmake==3.29.2
colorful==0.5.6
dataclasses-json==0.6.4
Deprecated==1.2.14
distlib==0.3.8
dm-tree==0.1.8
dnspython==2.6.1
email-validator==2.1.1
exceptiongroup==1.2.1
Farama-Notifications==0.0.4
fastapi==0.110.2
filelock==3.13.4
frozenlist==1.4.1
fsspec==2024.3.1
google-api-core==2.18.0
google-auth==2.29.0
googleapis-common-protos==1.63.0
gpustat==1.1.1
greenlet==3.0.3
grpcio==1.43.0
gunicorn==22.0.0
gymnasium==0.28.1
h11==0.14.0
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
huggingface-hub==0.22.2
idna==3.7
imageio==2.34.0
importlib-metadata==7.0.0
importlib-resources==6.4.0
itsdangerous==2.2.0
jax-jumpy==1.0.0
Jinja2==3.1.3
joblib==1.4.0
jsonpatch==1.33
jsonpointer==2.4
jsonschema==4.21.1
jsonschema-specifications==2023.12.1
langchain==0.2.14
langchain-community==0.2.12
langchain-core==0.2.32
langchain-text-splitters==0.2.2
langsmith==0.1.49
lazy-loader==0.4
lit==18.1.3
lz4==4.3.3
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.21.1
mdurl==0.1.2
mpmath==1.3.0
msgpack==1.0.8
multidict==6.0.5
mypy-extensions==1.0.0
networkx==3.1
nltk==3.8.1
numpy==1.24.4
nvidia-cublas-cu12==12.1.3.1
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==8.9.2.26
nvidia-cufft-cu12==11.0.2.54
nvidia-curand-cu12==10.3.2.106
nvidia-cusolver-cu12==11.4.5.107
nvidia-cusparse-cu12==12.1.0.106
nvidia-ml-py==12.535.133
nvidia-nccl-cu12==2.19.3
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.1.105
opencensus==0.11.4
opencensus-context==0.1.3
opentelemetry-api==1.24.0
opentelemetry-exporter-otlp==1.24.0
opentelemetry-exporter-otlp-proto-common==1.24.0
opentelemetry-exporter-otlp-proto-grpc==1.24.0
opentelemetry-exporter-otlp-proto-http==1.24.0
opentelemetry-proto==1.24.0
opentelemetry-sdk==1.24.0
opentelemetry-semantic-conventions==0.45b0
orjson==3.10.1
packaging==23.2
pandas==2.0.0
pillow==10.3.0
pkgutil-resolve-name==1.3.10
platformdirs==4.2.0
prometheus-client==0.13.1
proto-plus==1.23.0
protobuf==3.20.3
psutil==5.9.8
py-spy==0.3.14
pyarrow==15.0.2
pyasn1==0.6.0
pyasn1-modules==0.4.0
pydantic==2.7.0
pydantic-core==2.18.1
pydantic-extra-types==2.6.0
pydantic-settings==2.2.1
pygments==2.17.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.9
pytz==2024.1
PyWavelets==1.4.1
PyYAML==6.0.1
ray[all]==2.10.0
ray-cpp==2.10.0
referencing==0.34.0
regex==2024.4.16
requests==2.31.0
rich==13.7.1
rpds-py==0.18.0
rsa==4.9
safetensors==0.4.3
scikit-image==0.21.0
scikit-learn==1.3.2
scipy==1.10.1
sentence-transformers==2.7.0
sentencepiece==0.2.0
shellingham==1.5.4
six==1.16.0
smart-open==7.0.4
sniffio==1.3.1
SQLAlchemy==2.0.29
starlette==0.37.2
sympy==1.12
tenacity==8.2.3
tensorboardX==*******
threadpoolctl==3.4.0
tifffile==2023.7.10
tokenizers==0.19.1
torch==2.2.2
torchvision==0.17.2
tqdm==4.66.2
transformers==4.40.0
triton==2.2.0
typer==0.12.3
typing-extensions==4.11.0
typing-inspect==0.9.0
tzdata==2024.1
ujson==5.9.0
urllib3==2.2.1
uvicorn==0.29.0
uvloop==0.19.0
virtualenv==20.25.3
watchfiles==0.21.0
wcwidth==0.2.13
websockets==12.0
wrapt==1.16.0
yarl==1.9.4
zipp==3.18.1
